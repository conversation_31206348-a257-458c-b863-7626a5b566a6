"use client";

import { authClient } from "@/lib/auth/client";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useMemo, useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { ShopInfoType } from "@/app/[locale]/data";
import Text from "@/components/Text";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { useAutoAnimate } from "@formkit/auto-animate/react";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { submitOrderAction } from "./actions";
import { useCartStore } from "./cartStore";
import {
  CheckoutFormData,
  createCheckoutFormSchema,
  paymentMethods,
} from "./model";
import { getPaymentDetails } from "./paymentDetails";
import SuccessfulOrderMessage from "./SuccessfulOrderMessage";

type CheckoutFormProps = {
  className?: string;
  selectedShop: ShopInfoType | null;
};

const CartCheckoutForm = ({ className, selectedShop }: CheckoutFormProps) => {
  const t = useTranslations("CheckoutForm");
  const [parent] = useAutoAnimate();
  const [isPending, startTransition] = useTransition();
  const [submittedOrderId, setSubmittedOrderId] = useState<string | null>(null);

  const { data: session } = authClient.useSession();
  const user = session?.user;
  const { items, clearCart } = useCartStore();

  const checkoutFormSchema = useMemo(() => {
    return createCheckoutFormSchema((key: string) => {
      // Type-safe translation lookup
      const errorKeys: Record<string, any> = {
        "errors.invalidEmail": t("errors.invalidEmail"),
        "errors.selectPaymentMethod": t("errors.selectPaymentMethod"),
        "errors.storeNameRequired": t("errors.storeNameRequired"),
        "errors.storeAddressRequired": t("errors.storeAddressRequired"),
        "errors.invalidPhoneNumber": t("errors.invalidPhoneNumber"),
        "errors.firstNameMinLength": t("errors.firstNameMinLength"),
        "errors.firstNameMaxLength": t("errors.firstNameMaxLength"),
        "errors.firstNameNoSpaces": t("errors.firstNameNoSpaces"),
        "errors.lastNameMinLength": t("errors.lastNameMinLength"),
        "errors.lastNameMaxLength": t("errors.lastNameMaxLength"),
        "errors.lastNameNoSpaces": t("errors.lastNameNoSpaces"),
      };
      return errorKeys[key] || key;
    });
  }, [t]);

  const defaultValues = useMemo((): CheckoutFormData => {
    return {
      firstName: user?.name?.split(" ")[0] || "",
      lastName: user?.name?.split(" ")[1] || "",
      phone: user?.phoneNumber || "",
      email: user?.email || "",
      paymentMethod: paymentMethods.BANK_TRANSFER,
    };
  }, [user]);

  const form = useForm<z.infer<typeof checkoutFormSchema>>({
    resolver: zodResolver(checkoutFormSchema),
    defaultValues,
  });

  const onSubmit = (data: CheckoutFormData) => {
    if (!selectedShop) {
      toast.error(t("errors.selectStoreFirst"));
      return;
    }

    startTransition(async () => {
      try {
        const result = await submitOrderAction({
          ...data,
          storeName: selectedShop.name,
          storeAddress: selectedShop.address,
          items,
        });

        if (result.success) {
          setSubmittedOrderId(result.data);
        } else {
          toast.error(result.data || t("errors.orderSubmissionError"));
        }
      } catch (error) {
        console.error("Error submitting order:", error);
        toast.error(t("errors.unexpectedError"));
      }
    });
  };

  const paymentMethodValue = form.watch("paymentMethod");

  const isSubmitDisabled = !selectedShop || isPending;

  const handleClose = () => {
    clearCart();
    setSubmittedOrderId(null);
  };

  if (submittedOrderId) {
    return (
      <SuccessfulOrderMessage
        className={className}
        orderId={submittedOrderId}
        paymentMethod={form.getValues("paymentMethod")}
        onClose={handleClose}
      />
    );
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn(
          "grid grid-cols-1 gap-8 lowercase md:grid-cols-2 md:gap-12",
          className,
        )}
      >
        <section className="flex flex-col gap-4">
          <FormField
            control={form.control}
            name="paymentMethod"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("paymentMethodLabel")}</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger id="checkout">
                      <SelectValue
                        placeholder={t("selectPaymentPlaceholder")}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value={paymentMethods.BANK_TRANSFER}>
                      {t("paymentMethods.BANK_TRANSFER")}
                    </SelectItem>
                    <SelectItem
                      value={paymentMethods.MULTICAIXA_EXPRESS}
                      disabled
                    >
                      {t("paymentMethods.MULTICAIXA_EXPRESS")}
                    </SelectItem>
                    <SelectItem value={paymentMethods.PAY_IN_STORE} disabled>
                      {t("paymentMethods.PAY_IN_STORE")}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <article ref={parent} className="flex flex-col gap-2">
            {selectedShop ? (
              <div className="mb-2 border-b pb-2">
                <Text size="sm" className="font-semibold">
                  {t("storeInformation")}
                </Text>
                <span className="flex gap-2">
                  <Text size="sm">{t("storeName")}</Text>
                  <Text as="p" size="sm" className="font-semibold">
                    {selectedShop.name}
                  </Text>
                </span>
                <span className="flex gap-2">
                  <Text size="sm">{t("storeAddress")}</Text>
                  <Text as="p" size="sm" className="font-semibold">
                    {selectedShop.address}
                  </Text>
                </span>
                <span className="flex gap-2">
                  <Text size="sm">{t("storePhone")}</Text>
                  <Text as="p" size="sm" className="font-semibold">
                    {selectedShop.phone}
                  </Text>
                </span>
              </div>
            ) : (
              <div className="mb-2 border-b pb-2">
                <Text size="sm" className="text-secondary font-semibold">
                  {t("selectStoreFirst")}
                </Text>
              </div>
            )}
            {paymentMethodValue &&
              getPaymentDetails(t, paymentMethodValue).map((detail, index) => (
                <span key={index} className="flex gap-2">
                  <Text size="sm">
                    {detail.label}
                    {detail.value && ":"}
                  </Text>
                  {detail?.value && (
                    <Text
                      as="p"
                      size="sm"
                      className="font-semibold normal-case"
                    >
                      {detail.value}
                    </Text>
                  )}
                </span>
              ))}
          </article>
        </section>

        <section className="flex flex-col gap-8">
          <div className="flex flex-col gap-2 self-end">
            {/* <Text as="h6" className="text-right">
              {t("orderReferenceLabel")}:{" "}
              <b className="font-semibold">{orderReference}</b>
            </Text> */}
            <Text as="p" size="sm" className="-mb-4 text-right font-semibold">
              {t("requiredFieldsLabel")}
            </Text>
          </div>
          <span className="max-xs:flex-col flex gap-8 *:flex-1 sm:gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("firstNameLabel")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("firstNamePlaceholder")}
                      maxLength={30}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("lastNameLabel")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("lastNamePlaceholder")}
                      maxLength={30}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </span>
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("phoneLabel")}</FormLabel>
                <FormControl>
                  <Input
                    type="tel"
                    placeholder={t("phonePlaceholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("emailLabel")}</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder={t("emailPlaceholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            type="submit"
            className={cn("w-full", isSubmitDisabled && "opacity-80")}
            disabled={isSubmitDisabled}
            title={t("payNowButton")}
          >
            {t("payNowButton")}
            {isPending && <Loader2 className="mt-1 size-4 animate-spin" />}
          </Button>
        </section>
      </form>
    </Form>
  );
};

export default CartCheckoutForm;
