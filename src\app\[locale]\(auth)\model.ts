import { TFunctionUser } from "@/i18n/types";
import { authClient } from "@/lib/auth/client";
import { z } from "zod";

// Create schema factory functions that accept translation function
export const createPhoneNumberSchema = (t: (key: string) => string) =>
  z
    .string()
    .refine(
      (value) => !value || /^\+\d{3}\d{9}$/.test(value),
      t("errors.invalidPhoneNumber"),
    );

export const createProfileSchema = (t: (key: string) => string) =>
  z.object({
    firstName: z
      .string()
      .min(2, { message: t("errors.firstNameMinLength") })
      .max(30, { message: t("errors.firstNameMaxLength") })
      .refine((value) => !/\s/.test(value), t("errors.firstNameNoSpaces")),
    lastName: z
      .string()
      .min(2, { message: t("errors.lastNameMinLength") })
      .max(30, { message: t("errors.lastNameMaxLength") })
      .refine((value) => !/\s/.test(value), t("errors.lastNameNoSpaces")),
    phoneNumber: createPhoneNumberSchema(t).optional().nullable(),
  });

// Legacy exports for backward compatibility (using Portuguese messages)
export const phoneNumberSchema = createPhoneNumberSchema((key) => {
  const messages: Record<string, string> = {
    "errors.invalidPhoneNumber": "Número de telefone inválido",
  };
  return messages[key] || key;
});

export const profileSchema = createProfileSchema((key) => {
  const messages: Record<string, string> = {
    "errors.firstNameMinLength": "Nome deve ter pelo menos 2 caracteres.",
    "errors.firstNameMaxLength": "Nome deve ter no máximo 30 caracteres.",
    "errors.firstNameNoSpaces": "Espaços não são permitidos",
    "errors.lastNameMinLength": "Sobrenome deve ter pelo menos 2 caracteres.",
    "errors.lastNameMaxLength": "Sobrenome deve ter no máximo 30 caracteres.",
    "errors.lastNameNoSpaces": "Espaços não são permitidos",
    "errors.invalidPhoneNumber": "Número de telefone inválido",
  };
  return messages[key] || key;
});
export type ProfileFormType = z.infer<typeof profileSchema>;

// Create schema factory functions that accept translation function
export const createSignOnSchema = (t: (key: string) => string) =>
  z.object({
    email: z.string().email({ message: t("errors.invalidEmailAddress") }),
  });

export const createOtpSchema = (t: (key: string) => string) => {
  const signOnSchema = createSignOnSchema(t);
  return signOnSchema.extend({
    otp: z
      .string()
      .min(1, t("errors.otpRequired"))
      .max(6, t("errors.invalidOtp")),
  });
};

// Legacy exports for backward compatibility (using Portuguese messages)
export const signOnSchema = createSignOnSchema((key) => {
  const messages: Record<string, string> = {
    "errors.invalidEmailAddress": "Endereço de email inválido",
  };
  return messages[key] || key;
});

export const otpSchema = createOtpSchema((key) => {
  const messages: Record<string, string> = {
    "errors.invalidEmailAddress": "Endereço de email inválido",
    "errors.otpRequired": "OTP é obrigatório",
    "errors.invalidOtp": "OTP inválido",
  };
  return messages[key] || key;
});

export type SignOnFormType = z.infer<typeof signOnSchema>;
export type OtpFormType = z.infer<typeof otpSchema>;

const UserRoles = ["admin", "user"] as const;

export const userSchema = (t: TFunctionUser) =>
  profileSchema.extend({
    id: z.string(),
    email: z.string().email({ message: t("emailInvalid") }),
    role: z.enum(UserRoles, { message: t("roleInvalid") }),
    banned: z.boolean().optional().default(false),
    createdAt: z.date(),
    updatedAt: z.date(),
  });

export type UserType = z.infer<ReturnType<typeof userSchema>>;

export const fromAuthToUser = ({
  authUser,
  t,
}: {
  authUser: NonNullable<
    ReturnType<typeof authClient.useSession>["data"]
  >["user"];
  t: TFunctionUser;
}) => {
  if (!authUser) throw new Error("User data is not available");

  const user = {
    ...authUser,
    firstName: authUser.name?.split(" ")[0] ?? "",
    lastName: authUser.name?.split(" ")[1] ?? "",
  };

  return userSchema(t).parse(user);
};
