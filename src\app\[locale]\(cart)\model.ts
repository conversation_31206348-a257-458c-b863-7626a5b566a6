import { z } from "zod";
import { createPhoneNumberSchema, createProfileSchema } from "../(auth)/model";
import { PaymentMethod } from "@/generated/prisma";

export const paymentMethods = PaymentMethod;

// Create schema factory functions that accept translation function
export const createCheckoutFormSchema = (t: (key: string) => string) => {
  const profileSchema = createProfileSchema(t);
  const phoneNumberSchema = createPhoneNumberSchema(t);

  return profileSchema
    .pick({
      firstName: true,
      lastName: true,
    })
    .extend({
      phone: phoneNumberSchema,
      email: z.string().email({ message: t("errors.invalidEmail") }),
      paymentMethod: z.nativeEnum(paymentMethods, {
        message: t("errors.selectPaymentMethod"),
      }),
    });
};

export const createSubmitOrderSchema = (t: (key: string) => string) => {
  const checkoutFormSchema = createCheckoutFormSchema(t);

  return checkoutFormSchema.extend({
    storeName: z.string().min(1, { message: t("errors.storeNameRequired") }),
    storeAddress: z
      .string()
      .min(1, { message: t("errors.storeAddressRequired") }),
    items: z.array(
      z.object({
        name: z.string(),
        price: z.number(),
        size: z.object({
          euSize: z.string(),
        }),
        quantity: z.number(),
      }),
    ),
  });
};

// Legacy exports for backward compatibility (using Portuguese messages)
export const checkoutFormSchema = createCheckoutFormSchema((key) => {
  const messages: Record<string, string> = {
    "errors.invalidEmail": "Email inválido.",
    "errors.selectPaymentMethod": "Selecione um método de pagamento.",
    "errors.storeNameRequired": "Nome da loja é obrigatório.",
    "errors.storeAddressRequired": "Endereço da loja é obrigatório.",
    "errors.invalidPhoneNumber": "Número de telefone inválido",
    "errors.firstNameMinLength": "Nome deve ter pelo menos 2 caracteres.",
    "errors.firstNameMaxLength": "Nome deve ter no máximo 30 caracteres.",
    "errors.firstNameNoSpaces": "Espaços não são permitidos",
    "errors.lastNameMinLength": "Sobrenome deve ter pelo menos 2 caracteres.",
    "errors.lastNameMaxLength": "Sobrenome deve ter no máximo 30 caracteres.",
    "errors.lastNameNoSpaces": "Espaços não são permitidos",
  };
  return messages[key] || key;
});

export const submitOrderSchema = createSubmitOrderSchema((key) => {
  const messages: Record<string, string> = {
    "errors.invalidEmail": "Email inválido.",
    "errors.selectPaymentMethod": "Selecione um método de pagamento.",
    "errors.storeNameRequired": "Nome da loja é obrigatório.",
    "errors.storeAddressRequired": "Endereço da loja é obrigatório.",
    "errors.invalidPhoneNumber": "Número de telefone inválido",
    "errors.firstNameMinLength": "Nome deve ter pelo menos 2 caracteres.",
    "errors.firstNameMaxLength": "Nome deve ter no máximo 30 caracteres.",
    "errors.firstNameNoSpaces": "Espaços não são permitidos",
    "errors.lastNameMinLength": "Sobrenome deve ter pelo menos 2 caracteres.",
    "errors.lastNameMaxLength": "Sobrenome deve ter no máximo 30 caracteres.",
    "errors.lastNameNoSpaces": "Espaços não são permitidos",
  };
  return messages[key] || key;
});

export type CheckoutFormData = z.infer<typeof checkoutFormSchema>;
export type SubmitOrderData = z.infer<typeof submitOrderSchema>;
