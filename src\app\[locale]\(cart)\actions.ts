"use server";

// import { CartItem } from "@/app/[locale]/(cart)/cartStore";
// import { auth } from "@/lib/auth/server";
import { orderConfirmationEmailTemplate, sendEmail } from "@/lib/mailer";
import prisma from "@/lib/prisma";
import { revalidateTag } from "next/cache";
// import { headers } from "next/headers";
import ShortUniqueId from "short-unique-id";
import { z } from "zod";
import { getUserIfExists } from "../(auth)/user-dal";
import { ORDER_CACHE_KEY } from "../(order)/actions";
import { SubmitOrderData, createSubmitOrderSchema } from "./model";
import { getTranslations } from "next-intl/server";
// import { LOCALES } from "@/i18n/routing";

// export const getUserCart = async () => {
//   try {
//     const session = await auth.api.getSession({ headers: await headers() });

//     if (!session?.user.id) return null;

//     const cart = await prisma.cart.findUnique({
//       where: {
//         userId: session.user.id,
//       },
//     });

//     return cart;
//   } catch (error) {
//     console.error("Error fetching user cart:", error);
//     return null;
//   }
// };

// export const saveCart = async (cartItems: CartItem[]) => {
//   try {
//     const session = await auth.api.getSession({ headers: await headers() });
//     if (!session?.user.id) return null;

//     const cart = await prisma.cart.upsert({
//       where: {
//         userId: session.user.id,
//       },
//       update: {
//         items: cartItems,
//       },
//       create: {
//         userId: session.user.id,
//         items: cartItems,
//       },
//     });

//     return cart;
//   } catch (error) {
//     console.error("Error saving user cart:", error);
//     return null;
//   }
// };

export const hasPendingOrder = async (email: string) => {
  const pendingOrder = await prisma.order.findFirst({
    where: {
      email,
      status: "PENDING",
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  return !!pendingOrder;
};

export const submitOrderAction = async (data: SubmitOrderData) => {
  const t = await getTranslations("OrderSubmission");
  const tCheckout = await getTranslations("CheckoutForm");

  try {
    const submitOrderSchema = createSubmitOrderSchema((key: string) => {
      // Type-safe translation lookup
      const errorKeys: Record<string, any> = {
        "errors.invalidEmail": tCheckout("errors.invalidEmail"),
        "errors.selectPaymentMethod": tCheckout("errors.selectPaymentMethod"),
        "errors.storeNameRequired": tCheckout("errors.storeNameRequired"),
        "errors.storeAddressRequired": tCheckout("errors.storeAddressRequired"),
        "errors.invalidPhoneNumber": tCheckout("errors.invalidPhoneNumber"),
        "errors.firstNameMinLength": tCheckout("errors.firstNameMinLength"),
        "errors.firstNameMaxLength": tCheckout("errors.firstNameMaxLength"),
        "errors.firstNameNoSpaces": tCheckout("errors.firstNameNoSpaces"),
        "errors.lastNameMinLength": tCheckout("errors.lastNameMinLength"),
        "errors.lastNameMaxLength": tCheckout("errors.lastNameMaxLength"),
        "errors.lastNameNoSpaces": tCheckout("errors.lastNameNoSpaces"),
      };
      return errorKeys[key] || key;
    });
    const validatedData = submitOrderSchema.parse(data);

    if (await hasPendingOrder(validatedData.email)) {
      return {
        success: false,
        data: t("pendingOrderError"),
      };
    }

    const total = validatedData.items.reduce(
      (acc, item) => acc + item.price * item.quantity,
      0,
    );

    const orderReference = new ShortUniqueId().formattedUUID(
      "Kur-$t0$s2$r4",
      new Date(),
    );

    const user = await getUserIfExists();

    await prisma.order.create({
      data: {
        ...validatedData,
        total,
        id: orderReference,
        userId: user?.id,
      },
    });

    const customerName = `${validatedData.firstName} ${validatedData.lastName}`;
    const emailItems = validatedData.items.map((item) => ({
      name: item.name,
      size: item.size.euSize,
      quantity: item.quantity,
      price: item.price,
    }));

    await sendEmail({
      to: validatedData.email,
      cc: process.env.SALES_EMAIL || "<EMAIL>",
      subject: `Confirmação do Pedido - ${orderReference}`,
      html: orderConfirmationEmailTemplate({
        orderReference,
        customerName,
        items: emailItems,
        total,
        paymentMethod: validatedData.paymentMethod,
        storeName: validatedData.storeName,
        storeAddress: validatedData.storeAddress,
      }),
    });

    revalidateTag(ORDER_CACHE_KEY);
    return { success: true, data: orderReference };
  } catch (error) {
    console.error("Error submitting order:", error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        data: t("validationError"),
      };
    }

    return {
      success: false,
      data: t("serverError"),
    };
  }
};
